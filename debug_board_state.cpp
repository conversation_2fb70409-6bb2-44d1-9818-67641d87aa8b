#include <iostream>
#include "include/common.h"
#include "include/board.h"
#include "include/protocol.h"
#include "include/ai_engine.h"

using namespace std;

int main() {
    cout << "=== 棋盘状态调试测试 ===" << endl;
    
    // 初始化
    initializeBoard();
    computerSide = BLACK;
    
    cout << "初始棋盘状态:" << endl;
    printBoard();
    
    cout << "\ncomputerSide = " << computerSide << " (BLACK=" << BLACK << ", WHITE=" << WHITE << ")" << endl;
    
    // 模拟对方移动：白方从A5移动到A3（可能是普通移动）
    Step opponentMove;
    opponentMove.start.x = 4; // A5的行坐标
    opponentMove.start.y = 0; // A5的列坐标
    opponentMove.end.x = 2;   // A3的行坐标
    opponentMove.end.y = 0;   // A3的列坐标
    
    cout << "\n=== 模拟对方移动 ===" << endl;
    cout << "对方移动: (" << opponentMove.start.x << "," << opponentMove.start.y 
         << ") -> (" << opponentMove.end.x << "," << opponentMove.end.y << ")" << endl;
    
    cout << "移动前起始位置棋子: " << Board[opponentMove.start.x][opponentMove.start.y] << endl;
    cout << "移动前目标位置棋子: " << Board[opponentMove.end.x][opponentMove.end.y] << endl;
    
    // 处理对方移动
    bool success = processOpponentMove(opponentMove);
    cout << "processOpponentMove结果: " << (success ? "成功" : "失败") << endl;
    
    if (!success) {
        cout << "尝试强制移动..." << endl;
        forceOpponentMove(opponentMove);
    }
    
    cout << "\n对方移动后棋盘状态:" << endl;
    printBoard();
    
    cout << "\n=== 生成己方移动 ===" << endl;
    Step myMove = generateBestMove();
    
    cout << "生成的己方移动: (" << myMove.start.x << "," << myMove.start.y 
         << ") -> (" << myMove.end.x << "," << myMove.end.y << ")" << endl;
    
    if (myMove.start.x >= 0 && myMove.start.y >= 0) {
        cout << "己方移动起始位置棋子: " << Board[myMove.start.x][myMove.start.y] << endl;
        cout << "己方移动目标位置棋子: " << Board[myMove.end.x][myMove.end.y] << endl;
        
        // 验证移动的合法性
        if (Board[myMove.start.x][myMove.start.y] == computerSide) {
            cout << "✓ 起始位置确实有己方棋子" << endl;
        } else {
            cout << "✗ 起始位置没有己方棋子！实际值: " << Board[myMove.start.x][myMove.start.y] << endl;
        }
    }
    
    return 0;
}
