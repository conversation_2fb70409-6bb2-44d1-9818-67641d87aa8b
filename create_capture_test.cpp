#include <iostream>
#include "include/common.h"
#include "include/board.h"
#include "include/protocol.h"

using namespace std;

int main() {
    // 初始化棋盘
    initializeBoard();
    computerSide = BLACK;
    
    cout << "初始棋盘:" << endl;
    printBoard();
    
    // 手动设置一个弧线吃子的场景
    // 假设白方棋子在(4,0)，黑方棋子在(2,2)
    // 白方可以通过弧线路径吃掉黑方棋子
    
    // 清空一些位置
    Board[4][1] = EMPTY;
    Board[4][2] = EMPTY;
    Board[4][3] = EMPTY;
    Board[3][0] = EMPTY;
    Board[3][1] = EMPTY;
    Board[3][2] = EMPTY;
    Board[3][3] = EMPTY;
    Board[2][0] = EMPTY;
    Board[2][1] = EMPTY;
    Board[2][3] = EMPTY;
    Board[2][4] = EMPTY;
    Board[2][5] = EMPTY;
    
    // 设置特定位置的棋子
    Board[2][2] = BLACK;  // 黑方棋子在(2,2)
    Board[4][0] = WHITE;  // 白方棋子在(4,0)
    
    cout << "\n设置后的棋盘（白方(4,0)可以吃黑方(2,2)）:" << endl;
    printBoard();
    
    // 测试白方吃黑方的移动
    Step captureMove;
    captureMove.start.x = 4;
    captureMove.start.y = 0;
    captureMove.end.x = 2;
    captureMove.end.y = 2;
    
    cout << "\n测试对方弧线吃子移动: (4,0) -> (2,2)" << endl;
    
    if (processOpponentMove(captureMove)) {
        cout << "弧线吃子处理成功!" << endl;
    } else {
        cout << "弧线吃子处理失败!" << endl;
    }
    
    cout << "\n吃子后的棋盘:" << endl;
    printBoard();
    
    return 0;
}
