#include "../include/protocol.h"
#include "../include/board.h"
#include "../include/ai_engine.h"

// 处理移动命令 - 完全重写以正确处理对方移动
void handleMoveCommand() {
    Step step;
    char message[256];

    scanf("%s", message);
    fflush(stdin);

    // 解析对手着法 - 修正坐标系统
    // 第一个字符是列(A-F)，第二个字符是行(A-F)
    step.start.y = message[0] - 'A';  // 列坐标
    step.start.x = message[1] - 'A';  // 行坐标
    step.end.y = message[2] - 'A';    // 列坐标
    step.end.x = message[3] - 'A';    // 行坐标

    // 调试模式开关
    bool debugMode = false;

    if (debugMode) {
        cout << "收到对方移动: " << message << endl;
        cout << "解析为: (" << step.start.x << "," << step.start.y
             << ") -> (" << step.end.x << "," << step.end.y << ")" << endl;
        cout << "移动前棋盘:" << endl;
        printBoard();
    }

    // 严格验证对手移动并正确更新棋盘
    if (processOpponentMove(step)) {
        if (debugMode) {
            cout << "对方移动处理成功" << endl;
            cout << "移动后棋盘:" << endl;
            printBoard();
        }
    } else {
        if (debugMode) {
            cout << "对方移动处理失败，尝试强制同步棋盘状态" << endl;
        }
        // 强制执行对方移动以保持棋盘同步
        forceOpponentMove(step);
        if (debugMode) {
            cout << "强制同步后棋盘:" << endl;
            printBoard();
        }
    }

    // 生成己方着法
    step = generateBestMove();

    // 完全重写的移动执行逻辑 - 最严格验证
    if (executeSafeMove(step)) {
        // 移动成功，输出着法
        cout << "move " << (char)(step.start.y + 'A') << (char)(step.start.x + 'A')
             << (char)(step.end.y + 'A') << (char)(step.end.x + 'A') << endl;
    } else {
        // 主移动失败，尝试安全备选移动
        Step safeMove = findAnyValidMove();
        if (executeSafeMove(safeMove)) {
            cout << "move " << (char)(safeMove.start.y + 'A') << (char)(safeMove.start.x + 'A')
                 << (char)(safeMove.end.y + 'A') << (char)(safeMove.end.x + 'A') << endl;
        }
        // 如果连安全移动都失败，不输出任何内容（让平台处理）
    }
}

// 处理对方移动的专用函数 - 完全重写以确保棋盘状态正确同步
bool processOpponentMove(Step opponentMove) {
    bool debugMode = false; // 调试模式（发布时设为false）

    if (debugMode) {
        cout << "=== 处理对方移动开始 ===" << endl;
        cout << "移动: (" << opponentMove.start.x << "," << opponentMove.start.y
             << ") -> (" << opponentMove.end.x << "," << opponentMove.end.y << ")" << endl;
        cout << "移动前棋盘状态:" << endl;
        printBoard();
    }

    // 第一步：验证坐标有效性
    if (opponentMove.start.x < 0 || opponentMove.start.x >= BOARD_SIZE ||
        opponentMove.start.y < 0 || opponentMove.start.y >= BOARD_SIZE ||
        opponentMove.end.x < 0 || opponentMove.end.x >= BOARD_SIZE ||
        opponentMove.end.y < 0 || opponentMove.end.y >= BOARD_SIZE) {
        if (debugMode) {
            cout << "✗ 对方移动坐标无效" << endl;
        }
        return false;
    }

    // 第二步：检查起始位置是否有对方棋子
    int startPiece = Board[opponentMove.start.x][opponentMove.start.y];
    int opponentSide = computerSide ^ 1;

    if (startPiece != opponentSide) {
        if (debugMode) {
            cout << "✗ 对方移动起始位置没有对方棋子" << endl;
            cout << "  起始位置(" << opponentMove.start.x << "," << opponentMove.start.y
                 << ")实际值: " << startPiece << ", 期望对方棋子: " << opponentSide << endl;
        }
        return false;
    }

    // 第三步：检查起始和目标位置不能相同
    if (opponentMove.start.x == opponentMove.end.x && opponentMove.start.y == opponentMove.end.y) {
        if (debugMode) {
            cout << "✗ 对方移动起始和目标位置相同" << endl;
        }
        return false;
    }

    // 第四步：判断移动类型并执行
    int targetPiece = Board[opponentMove.end.x][opponentMove.end.y];

    if (targetPiece == EMPTY) {
        // 这是普通移动
        if (debugMode) {
            cout << "检查对方普通移动..." << endl;
        }

        // 验证普通移动的合法性（8方向一格）
        int dx = abs(opponentMove.end.x - opponentMove.start.x);
        int dy = abs(opponentMove.end.y - opponentMove.start.y);

        if ((dx == 1 && dy == 0) || (dx == 0 && dy == 1) || (dx == 1 && dy == 1)) {
            // 执行普通移动 - 立即更新棋盘状态
            Board[opponentMove.end.x][opponentMove.end.y] = startPiece;
            Board[opponentMove.start.x][opponentMove.start.y] = EMPTY;

            if (debugMode) {
                cout << "✓ 对方普通移动执行成功" << endl;
                cout << "移动后棋盘状态:" << endl;
                printBoard();
            }
            return true;
        } else {
            if (debugMode) {
                cout << "✗ 对方普通移动不符合8方向一格规则 (dx=" << dx << ", dy=" << dy << ")" << endl;
            }
            return false;
        }
    }
    else if (targetPiece == computerSide) {
        // 对方吃掉己方棋子 - 必须是弧线吃子
        if (debugMode) {
            cout << "检查对方弧线吃子..." << endl;
            cout << "  目标位置有己方棋子: " << targetPiece << endl;
        }

        // 临时切换computerSide来使用canCapture函数
        int originalSide = computerSide;
        computerSide = opponentSide;

        bool canEat = canCapture(opponentMove.start, opponentMove.end);

        // 立即恢复computerSide
        computerSide = originalSide;

        if (canEat) {
            if (debugMode) {
                cout << "✓ 对方弧线吃子合法，执行吃子" << endl;
                cout << "  己方棋子 " << targetPiece << " 在位置("
                     << opponentMove.end.x << "," << opponentMove.end.y << ")被吃掉" << endl;
            }

            // 执行弧线吃子 - 立即更新棋盘状态
            // 对方棋子移动到目标位置，己方棋子被清除
            Board[opponentMove.end.x][opponentMove.end.y] = startPiece;
            Board[opponentMove.start.x][opponentMove.start.y] = EMPTY;

            if (debugMode) {
                cout << "✓ 弧线吃子执行完成，棋盘状态已更新" << endl;
                cout << "移动后棋盘状态:" << endl;
                printBoard();
            }
            return true;
        } else {
            if (debugMode) {
                cout << "✗ 对方弧线吃子不合法" << endl;
            }
            return false;
        }
    }
    else {
        // 对方尝试移动到对方棋子位置 - 这是不可能的
        if (debugMode) {
            cout << "✗ 对方尝试移动到对方棋子位置，这是不可能的" << endl;
            cout << "  目标位置棋子: " << targetPiece << ", 对方棋子: " << opponentSide << endl;
        }
        return false;
    }
}

// 强制执行对方移动以保持棋盘同步 - 改进版本
void forceOpponentMove(Step opponentMove) {
    bool debugMode = true;

    if (debugMode) {
        cout << "=== 强制执行对方移动 ===" << endl;
        cout << "移动: (" << opponentMove.start.x << "," << opponentMove.start.y
             << ") -> (" << opponentMove.end.x << "," << opponentMove.end.y << ")" << endl;
        cout << "强制移动前棋盘状态:" << endl;
        printBoard();
    }

    // 基本边界检查
    if (opponentMove.start.x < 0 || opponentMove.start.x >= BOARD_SIZE ||
        opponentMove.start.y < 0 || opponentMove.start.y >= BOARD_SIZE ||
        opponentMove.end.x < 0 || opponentMove.end.x >= BOARD_SIZE ||
        opponentMove.end.y < 0 || opponentMove.end.y >= BOARD_SIZE) {
        if (debugMode) {
            cout << "✗ 强制移动：坐标超出边界，跳过" << endl;
        }
        return;
    }

    // 检查起始位置是否有棋子
    int movingPiece = Board[opponentMove.start.x][opponentMove.start.y];
    if (movingPiece == EMPTY) {
        if (debugMode) {
            cout << "✗ 强制移动：起始位置没有棋子，跳过" << endl;
        }
        return;
    }

    if (debugMode) {
        cout << "移动棋子: " << movingPiece << " 从(" << opponentMove.start.x << "," << opponentMove.start.y
             << ") 到(" << opponentMove.end.x << "," << opponentMove.end.y << ")" << endl;
        cout << "目标位置原有棋子: " << Board[opponentMove.end.x][opponentMove.end.y] << endl;
    }

    // 强制执行移动，确保棋盘状态同步
    // 无论目标位置有什么棋子，都会被覆盖（这是强制同步的目的）
    Board[opponentMove.end.x][opponentMove.end.y] = movingPiece;
    Board[opponentMove.start.x][opponentMove.start.y] = EMPTY;

    if (debugMode) {
        cout << "✓ 强制移动执行完成，棋盘状态已同步" << endl;
        cout << "强制移动后棋盘状态:" << endl;
        printBoard();
    }
}

// 基于实时棋盘状态的安全移动执行函数 - 加强版本
bool executeSafeMove(Step step) {
    bool debugMode = false; // 调试模式（发布时设为false）

    if (debugMode) {
        cout << "=== 执行己方移动开始 ===" << endl;
        cout << "尝试移动: (" << step.start.x << "," << step.start.y
             << ") -> (" << step.end.x << "," << step.end.y << ")" << endl;
        cout << "移动前棋盘状态:" << endl;
        printBoard();
    }

    // 第一层验证：坐标有效性
    if (step.start.x < 0 || step.start.x >= BOARD_SIZE ||
        step.start.y < 0 || step.start.y >= BOARD_SIZE ||
        step.end.x < 0 || step.end.x >= BOARD_SIZE ||
        step.end.y < 0 || step.end.y >= BOARD_SIZE) {
        if (debugMode) {
            cout << "✗ 移动失败：坐标超出边界" << endl;
        }
        return false;
    }

    // 第二层验证：检查当前棋盘状态
    int startPiece = Board[step.start.x][step.start.y];
    int targetPiece = Board[step.end.x][step.end.y];

    if (debugMode) {
        cout << "起始位置 (" << step.start.x << "," << step.start.y << ") 的棋子: "
             << startPiece << " (己方:" << computerSide << ")" << endl;
        cout << "目标位置 (" << step.end.x << "," << step.end.y << ") 的棋子: "
             << targetPiece << " (对方:" << (computerSide ^ 1) << ")" << endl;
    }

    // 第三层验证：起始位置必须有己方棋子
    if (startPiece != computerSide) {
        if (debugMode) {
            cout << "✗ 移动失败：起始位置没有己方棋子！" << endl;
            cout << "  实际值: " << startPiece << ", 期望己方棋子: " << computerSide << endl;
            cout << "  这可能是因为该棋子已被对方吃掉" << endl;
        }
        return false;
    }

    // 第四层验证：不能移动到相同位置
    if (step.start.x == step.end.x && step.start.y == step.end.y) {
        if (debugMode) {
            cout << "✗ 移动失败：起始和目标位置相同" << endl;
        }
        return false;
    }

    // 第五层验证：不能移动到己方棋子位置
    if (targetPiece == computerSide) {
        if (debugMode) {
            cout << "✗ 移动失败：目标位置有己方棋子，不能移动到己方棋子位置" << endl;
        }
        return false;
    }

    // 第六层验证：检查移动类型的合法性
    bool canMove = false;
    string moveType = "";

    // 检查普通移动
    if (targetPiece == EMPTY) {
        if (isValidMove(step.start, step.end)) {
            canMove = true;
            moveType = "普通移动";
        } else {
            if (debugMode) {
                cout << "✗ 普通移动验证失败" << endl;
            }
        }
    }
    // 检查吃子移动
    else if (targetPiece == (computerSide ^ 1)) {
        if (canCapture(step.start, step.end)) {
            canMove = true;
            moveType = "弧线吃子";
        } else {
            if (debugMode) {
                cout << "✗ 弧线吃子验证失败" << endl;
            }
        }
    }

    if (!canMove) {
        if (debugMode) {
            cout << "✗ 移动失败：不符合移动规则" << endl;
        }
        return false;
    }

    // 第七层验证：执行移动
    if (debugMode) {
        cout << "✓ 执行" << moveType << "..." << endl;
    }

    // 立即更新棋盘状态
    Board[step.end.x][step.end.y] = startPiece;
    Board[step.start.x][step.start.y] = EMPTY;

    if (debugMode) {
        cout << "✓ 己方移动执行成功！" << endl;
        cout << "移动后棋盘状态:" << endl;
        printBoard();
    }
    return true;
}

// 处理新游戏命令
void handleNewCommand() {
    Step step;
    char message[256];
    
    scanf("%s", message);
    fflush(stdin);

    if (strcmp(message, "black") == 0) {
        computerSide = BLACK;
    } else {
        computerSide = WHITE;
    }

    // 初始化棋局
    initializeBoard();
    gameStarted = 1;

    if (computerSide == BLACK) {
        // 黑方先手，生成第一手着法
        step = generateBestMove();

        // 使用安全移动执行函数
        if (executeSafeMove(step)) {
            // 移动成功，输出着法
            cout << "move " << (char)(step.start.y + 'A') << (char)(step.start.x + 'A')
                 << (char)(step.end.y + 'A') << (char)(step.end.x + 'A') << endl;
        } else {
            // 主移动失败，尝试安全备选移动
            Step safeMove = findAnyValidMove();
            if (executeSafeMove(safeMove)) {
                cout << "move " << (char)(safeMove.start.y + 'A') << (char)(safeMove.start.x + 'A')
                     << (char)(safeMove.end.y + 'A') << (char)(safeMove.end.x + 'A') << endl;
            }
        }
    }
}

// 处理错误命令
void handleErrorCommand() {
    Step step;
    fflush(stdin);
    // 重新生成着法
    step = generateBestMove();
    // 使用安全移动执行函数
    if (executeSafeMove(step)) {
        cout << "move " << (char)(step.start.y + 'A') << (char)(step.start.x + 'A')
             << (char)(step.end.y + 'A') << (char)(step.end.x + 'A') << endl;
    } else {
        // 主移动失败，尝试安全备选移动
        Step safeMove = findAnyValidMove();
        if (executeSafeMove(safeMove)) {
            cout << "move " << (char)(safeMove.start.y + 'A') << (char)(safeMove.start.x + 'A')
                 << (char)(safeMove.end.y + 'A') << (char)(safeMove.end.x + 'A') << endl;
        }
    }
}

// 处理名称查询
void handleNameQuery() {
    fflush(stdin);
    cout << "name SurakartaEngine" << endl;
}

// 处理游戏结束命令
void handleEndCommand() {
    fflush(stdin);
    gameStarted = 0;
}

// 处理退出命令
void handleQuitCommand() {
    fflush(stdin);
    cout << "Quit!" << endl;
}

// 主通信循环
int runProtocolLoop() {
    char message[256];

    // 初始化随机数种子
    srand((unsigned int)time(NULL));

    // 程序主循环
    while (1) {
        fflush(stdout);

        // 获取平台消息
        if (scanf("%s", message) != 1) {
            break; // 输入结束或出错时退出
        }

        // 分析命令
        if (strcmp(message, "move") == 0) {
            handleMoveCommand();
        }
        else if (strcmp(message, "new") == 0) {
            handleNewCommand();
        }
        else if (strcmp(message, "error") == 0) {
            handleErrorCommand();
        }
        else if (strcmp(message, "name?") == 0) {
            handleNameQuery();
        }
        else if (strcmp(message, "end") == 0) {
            handleEndCommand();
        }
        else if (strcmp(message, "quit") == 0) {
            handleQuitCommand();
            break;
        }
    }

    return 0;
}
