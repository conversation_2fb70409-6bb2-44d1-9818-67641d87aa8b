#include <iostream>
#include "include/common.h"
#include "include/board.h"
#include "include/protocol.h"
#include "include/ai_engine.h"
#include "include/arc_capture.h"

using namespace std;

int main() {
    cout << "=== 弧线吃子测试 ===" << endl;
    
    // 初始化
    initializeBoard();
    computerSide = BLACK;
    
    cout << "初始棋盘状态:" << endl;
    printBoard();
    
    // 打印弧线信息
    printArcInfo();
    
    // 创建一个弧线吃子的测试场景
    // 清空一些位置，设置特定的棋子布局
    
    // 清空棋盘中间区域
    for (int i = 2; i < 4; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            Board[i][j] = EMPTY;
        }
    }
    
    // 设置测试场景：白方棋子在左上角弧线上，黑方棋子在弧线的另一端
    Board[0][0] = WHITE;  // 白方棋子在左上角弧线起点
    Board[1][1] = BLACK;  // 黑方棋子在左上角弧线上
    
    // 清空其他位置确保路径畅通
    Board[0][1] = EMPTY;
    Board[1][0] = EMPTY;
    
    cout << "\n设置后的测试棋盘:" << endl;
    printBoard();
    
    // 测试白方吃黑方的弧线移动
    Step captureMove;
    captureMove.start.x = 0;
    captureMove.start.y = 0;
    captureMove.end.x = 1;
    captureMove.end.y = 1;
    
    cout << "\n测试对方弧线吃子移动: (0,0) -> (1,1)" << endl;
    
    // 设置computerSide为黑方，这样白方就是对方
    computerSide = BLACK;
    
    if (processOpponentMove(captureMove)) {
        cout << "弧线吃子处理成功!" << endl;
    } else {
        cout << "弧线吃子处理失败!" << endl;
    }
    
    cout << "\n吃子后的棋盘:" << endl;
    printBoard();
    
    // 测试己方移动生成
    cout << "\n=== 测试己方移动生成 ===" << endl;
    Step myMove = generateBestMove();
    
    cout << "生成的己方移动: (" << myMove.start.x << "," << myMove.start.y 
         << ") -> (" << myMove.end.x << "," << myMove.end.y << ")" << endl;
    
    return 0;
}
